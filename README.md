# Create BBY TEST Account - Windows User Account Creator

A C# GUI application designed to run in Windows PE (WinPE) environment to create user accounts in offline Windows 11 installations by directly manipulating the registry.

## Features

- **WinPE Compatible**: Designed to run in Windows PE environment
- **Offline Account Creation**: Creates user accounts in offline Windows installations
- **GUI Interface**: Easy-to-use Windows Forms interface
- **Administrator Support**: Option to create administrator accounts
- **Registry Manipulation**: Directly modifies SAM and SECURITY registry hives
- **Password Hashing**: Implements proper NT password hashing
- **Validation**: Validates Windows installation paths and user input

## Requirements

- Windows PE environment or Windows with administrator privileges
- .NET Framework 4.8
- Target Windows installation must be accessible (mounted drive)

## How It Works

The application works by:

1. **Loading Registry Hives**: Loads the SAM and SECURITY registry hives from the offline Windows installation
2. **SID Generation**: Generates appropriate Security Identifiers (SIDs) for new users
3. **Password Hashing**: Creates NT hashes for passwords (compatible with Windows authentication)
4. **Registry Modification**: Creates necessary registry entries for the user account
5. **Cleanup**: <PERSON>perly unloads registry hives and saves changes

## Usage

1. **Run the Application**: Launch `CreateBBYTESTAccount.exe` with administrator privileges
2. **Select Windows Path**: Browse to the Windows installation directory (e.g., `C:\Windows`)
3. **Enter User Details**:
   - Username (max 20 characters)
   - Password
   - Full Name (optional)
   - Administrator privileges (checkbox)
4. **Create Account**: Click "Create Account" to create the user
5. **Verification**: The account will be available when Windows boots normally

## Important Notes

### Security Considerations
- This tool requires administrator privileges
- It directly modifies system registry files
- Use only on systems you own or have permission to modify
- Always backup registry files before making changes

### WinPE Compatibility
- Targets .NET Framework 4.8 for maximum compatibility
- Uses Windows Forms (not WPF) for better WinPE support
- Minimal dependencies to reduce deployment complexity

### Limitations
- Only works with offline Windows installations
- Does not create user profiles (created on first login)
- Limited to local accounts (not domain accounts)
- Simplified SAM database manipulation (may not support all Windows features)

## Technical Details

### Registry Hives Modified
- **SAM**: Security Account Manager database containing user accounts
- **SECURITY**: Security policies and machine SID information

### Key Components
- **UserAccountCreator**: Main logic for account creation
- **RegistryHelper**: Registry hive loading and manipulation
- **SecurityHelper**: SID generation and password hashing
- **MainForm**: GUI interface

### Password Hashing
- Implements MD4-based NT hash algorithm
- Compatible with Windows authentication system
- Does not use deprecated LM hashes

## Building the Application

1. Open the solution in Visual Studio
2. Ensure .NET Framework 4.8 is installed
3. Build in Release mode for deployment
4. Copy the executable to your WinPE environment

## Deployment to WinPE

1. Build the application in Release mode
2. Copy `CreateBBYTESTAccount.exe` to your WinPE image
3. Ensure .NET Framework 4.8 is available in WinPE
4. Run with administrator privileges

## Troubleshooting

### Common Issues
- **Access Denied**: Ensure running with administrator privileges
- **Registry Load Failed**: Check Windows installation path is correct
- **Invalid Windows Path**: Verify SAM and SECURITY files exist in System32\config

### Error Messages
- **"Insufficient Privileges"**: Run as administrator or from WinPE
- **"Invalid Windows installation"**: Check the selected Windows directory
- **"Failed to load SAM hive"**: Registry files may be in use or corrupted

## Legal Disclaimer

This tool is provided for legitimate system administration purposes only. Users are responsible for:
- Ensuring they have proper authorization to modify target systems
- Complying with applicable laws and regulations
- Understanding the security implications of creating user accounts
- Backing up systems before making changes

## Support

This is a specialized tool for system administrators and IT professionals. Users should have:
- Understanding of Windows registry structure
- Knowledge of user account management
- Experience with WinPE environments
- Ability to troubleshoot registry-related issues

## Version History

- **v1.0**: Initial release with basic user account creation functionality
