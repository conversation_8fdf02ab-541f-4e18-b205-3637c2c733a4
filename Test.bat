@echo off
echo ========================================
echo Create BBY TEST Account - Test Runner
echo ========================================
echo.

echo Checking if application exists...
if not exist "bin\Release\CreateBBYTESTAccount.exe" (
    echo Application not found! Please build first using Deploy.bat
    pause
    exit /b 1
)

echo Application found: bin\Release\CreateBBYTESTAccount.exe
echo.

echo Checking for administrator privileges...
net session >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Not running as administrator!
    echo The application requires administrator privileges to work properly.
    echo Please run this script as administrator.
    echo.
)

echo Starting application...
echo.
echo IMPORTANT NOTES:
echo - This application modifies Windows registry files
echo - Only use on test systems or systems you own
echo - Always backup registry files before making changes
echo - Ensure the target Windows installation is not running
echo.

pause

start "" "bin\Release\CreateBBYTESTAccount.exe"

echo Application started. Check the GUI for operation status.
echo.
pause
